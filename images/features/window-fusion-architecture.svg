<svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="400" y="40" text-anchor="middle" font-size="24" font-weight="bold" fill="#333">窗口无界融合架构</text>
  
  <!-- 宿主桌面层 -->
  <rect x="50" y="80" width="700" height="150" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
  <text x="70" y="110" font-size="16" font-weight="bold" fill="#1976d2">国产桌面环境 (宿主系统)</text>
  
  <!-- 本地应用窗口 -->
  <rect x="80" y="130" width="120" height="80" fill="#fff" stroke="#666" stroke-width="1" rx="5"/>
  <text x="140" y="155" text-anchor="middle" font-size="12" fill="#333">本地应用</text>
  <text x="140" y="175" text-anchor="middle" font-size="12" fill="#333">窗口1</text>
  
  <rect x="220" y="130" width="120" height="80" fill="#fff" stroke="#666" stroke-width="1" rx="5"/>
  <text x="280" y="155" text-anchor="middle" font-size="12" fill="#333">本地应用</text>
  <text x="280" y="175" text-anchor="middle" font-size="12" fill="#333">窗口2</text>
  
  <!-- 融合的虚拟应用窗口 -->
  <rect x="360" y="130" width="120" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
  <text x="420" y="155" text-anchor="middle" font-size="12" fill="#2e7d32">传统应用</text>
  <text x="420" y="175" text-anchor="middle" font-size="12" fill="#2e7d32">融合窗口</text>
  
  <rect x="500" y="130" width="120" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
  <text x="560" y="155" text-anchor="middle" font-size="12" fill="#2e7d32">传统应用</text>
  <text x="560" y="175" text-anchor="middle" font-size="12" fill="#2e7d32">融合窗口</text>
  
  <!-- 窗口融合层 -->
  <rect x="50" y="260" width="700" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="10"/>
  <text x="70" y="290" font-size="16" font-weight="bold" fill="#f57c00">窗口融合层</text>
  <text x="70" y="315" font-size="14" fill="#e65100">• 窗口渲染优化</text>
  <text x="70" y="335" font-size="14" fill="#e65100">• 事件透传处理</text>
  <text x="300" y="315" font-size="14" fill="#e65100">• 拖拽操作支持</text>
  <text x="300" y="335" font-size="14" fill="#e65100">• 剪贴板同步</text>
  
  <!-- 虚拟化引擎 -->
  <rect x="50" y="390" width="700" height="100" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="10"/>
  <text x="70" y="420" font-size="16" font-weight="bold" fill="#7b1fa2">第五象限虚拟化引擎</text>
  <text x="70" y="445" font-size="14" fill="#4a148c">• GPU 直通加速</text>
  <text x="70" y="465" font-size="14" fill="#4a148c">• 内存优化管理</text>
  <text x="300" y="445" font-size="14" fill="#4a148c">• CPU 智能调度</text>
  <text x="300" y="465" font-size="14" fill="#4a148c">• 网络性能优化</text>
  
  <!-- 传统应用系统 -->
  <rect x="50" y="520" width="700" height="60" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="10"/>
  <text x="70" y="545" font-size="16" font-weight="bold" fill="#d32f2f">传统应用运行环境</text>
  <text x="70" y="565" font-size="14" fill="#c62828">Windows/Linux 应用程序</text>
  
  <!-- 连接线 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- 从虚拟化引擎到窗口融合层 -->
  <line x1="400" y1="390" x2="400" y2="360" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 从窗口融合层到桌面 -->
  <line x1="400" y1="260" x2="400" y2="230" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 从传统应用到虚拟化引擎 -->
  <line x1="400" y1="520" x2="400" y2="490" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
</svg>
