<svg width="800" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变 -->
  <defs>
    <!-- 对角线金色渐变 -->
    <linearGradient id="diagonal-gold-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color: #FFD700; stop-opacity: 1" />
      <stop offset="50%" style="stop-color: #FFC107; stop-opacity: 1" />
      <stop offset="100%" style="stop-color: #FFA500; stop-opacity: 1" />
    </linearGradient>
    <!-- 对角线银灰色渐变 -->
    <linearGradient id="diagonal-silver-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color: #C0C0C0; stop-opacity: 1" />
      <stop offset="50%" style="stop-color: #A9A9A9; stop-opacity: 1" />
      <stop offset="100%" style="stop-color: #808080; stop-opacity: 1" />
    </linearGradient>
    <!-- 光泽渐变 -->
    <linearGradient id="diagonal-shine-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color: white; stop-opacity: 0.2" />
      <stop offset="50%" style="stop-color: white; stop-opacity: 0.1" />
      <stop offset="100%" style="stop-color: white; stop-opacity: 0" />
    </linearGradient>

    <!-- 定义金色矩形原件 -->
    <symbol id="golden-rectangle">
      <rect width="100" height="100" fill="url(#diagonal-gold-gradient)" />
      <rect width="100" height="100" fill="url(#diagonal-shine-gradient)" />
    </symbol>

    <!-- 定义银灰色矩形原件 -->
    <symbol id="silver-rectangle">
      <rect width="100" height="100" fill="url(#diagonal-silver-gradient)" />
      <rect width="100" height="100" fill="url(#diagonal-shine-gradient)" />
    </symbol>
  </defs>
	<g transform="translate(50, 50)">
  <!-- 使用金色矩形原件 -->
  <use href="#golden-rectangle" x="200" y="0" />
  <use href="#golden-rectangle" x="300" y="0" />
  <use href="#golden-rectangle" x="400" y="0" />

  
  <!-- 使用银灰色矩形原件 -->
  <use href="#silver-rectangle" x="100" y="100" />
  <use href="#golden-rectangle" x="200" y="100" />
  <use href="#silver-rectangle" x="300" y="100" />
  <use href="#golden-rectangle" x="400" y="100" />
  <use href="#golden-rectangle" x="500" y="100" />

  <use href="#silver-rectangle" x="0" y="200" />
  <use href="#silver-rectangle" x="100" y="200" />
  <use href="#golden-rectangle" x="200" y="200" />
  <use href="#golden-rectangle" x="300" y="200" />
  <use href="#golden-rectangle" x="400" y="200" />
  <use href="#golden-rectangle" x="500" y="200" />
  <use href="#golden-rectangle" x="600" y="200" />
  
  <use href="#silver-rectangle" x="0" y="300" />
  <use href="#silver-rectangle" x="100" y="300" />
  <use href="#silver-rectangle" x="200" y="300" />
  <use href="#golden-rectangle" x="400" y="300" />
  <use href="#golden-rectangle" x="500" y="300" />
  <use href="#golden-rectangle" x="600" y="300" />
  
  <use href="#silver-rectangle" x="0" y="400" />
  <use href="#silver-rectangle" x="100" y="400" />
  <use href="#silver-rectangle" x="200" y="400" />
  <use href="#silver-rectangle" x="300" y="400" />
  <use href="#silver-rectangle" x="400" y="400" />
  <use href="#golden-rectangle" x="500" y="400" />
  <use href="#golden-rectangle" x="600" y="400" />
  
  <use href="#silver-rectangle" x="100" y="500" />
  <use href="#silver-rectangle" x="200" y="500" />
  <use href="#golden-rectangle" x="300" y="500" />
  <use href="#silver-rectangle" x="400" y="500" />
  <use href="#golden-rectangle" x="500" y="500" />
  
  <use href="#silver-rectangle" x="200" y="600" />
  <use href="#silver-rectangle" x="300" y="600" />
  <use href="#silver-rectangle" x="400" y="600" />
	</g>
</svg>